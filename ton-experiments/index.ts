import * as fs from 'node:fs';

import * as ton from "@ton/ton";
import * as tonCrypto from "@ton/crypto";
import * as dotenv from "dotenv";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const cmd = process.argv[2];

  const workchain = 0;
  const wc = {testOnly: true, bounceable: false};

  // ===========================================================

  if (cmd === 'create-mn') await createWallet(ton.WalletContractV5R1);

  async function createWallet(contractVersion: typeof ton.WalletContractV5R1) {
    console.log('--> Create contract with:', contractVersion);
    const mn = await tonCrypto.mnemonicNew();
    const mnFile = `ton_wallet_mnemonic_${+new Date()}`;
    fs.writeFileSync(mnFile, mn.join(','));
    console.log('--> Mnemonic file:', mnFile);
  }

  // ===========================================================

  if (cmd === 'balance') await getBalance(ton.WalletContractV5R1);
  if (cmd === 'balance-4') await getBalance(ton.WalletContractV4);

  async function getBalance(contractVersion: typeof ton.WalletContractV5R1 | typeof ton.WalletContractV4) {
    console.log('--> Get balance with contract version:', contractVersion);

    const PUBLIC_KEY = process.argv[3];

    const wallet = contractVersion.create({workchain, publicKey: Buffer.from(PUBLIC_KEY.replace('0x', ''), 'hex')});
    const contract = client.open(wallet);
    console.log('--> Check contract deployed:', await client.isContractDeployed(wallet.address));
    console.log('--> Address:', contract.address.toString(wc));
    console.log('--> Balance:', +((await contract.getBalance()).toString()) / 10 ** 9);
  }

  // ===========================================================

  if (cmd === 'broadcast-signed-tx') await sendSignedTxFromWalletService();

  async function sendSignedTxFromWalletService() {
    const SIGNED_TX_FROM_WALLET_SERVICE = process.argv[3];

    const [signedTxBuf, pubKeyBuf] = SIGNED_TX_FROM_WALLET_SERVICE.split(':pk_').map(i => Buffer.from(i.replace('0x', ''), 'hex'));
    const wallet = ton.WalletContractV5R1.create({publicKey: pubKeyBuf});
    const contract = client.open(wallet);

    console.log('--> Public Key Hex:', pubKeyBuf.toString('hex'));
    console.log('--> Public Key Base64:', pubKeyBuf.toString('base64'));
    console.log('--> Check contract deployed:', await client.isContractDeployed(wallet.address));
    console.log('--> Address:', contract.address.toString(wc), 'Raw:', contract.address.toRawString());
    console.log('--> Balance:', +((await contract.getBalance()).toString()) / 10 ** 9);
    console.log('--> Correct seqno:', await contract.getSeqno());

    // Create contract provider to for sending tx
    await contract.send(ton.Cell.fromBase64(signedTxBuf.toString('base64')))
      .then(() => console.log('--> Sent'))
      .catch(e => console.log(e.response.data));
  }

  // ===========================================================

  if (cmd === 'jetton-wallet') {
    const JETTON_MASTER_ADDRESS = process.argv[3];
    const USER_WALLET_ADDRESS = process.argv[4];

    console.log('--> Input: Jetton Master Address:', JETTON_MASTER_ADDRESS);
    console.log('--> Input: Account Address:', USER_WALLET_ADDRESS);

    // Load Jetton Master
    const km = ton.JettonMaster.create(ton.address(JETTON_MASTER_ADDRESS))
    const jmc = client.open(km);

    // Load Jetton Wallet
    const jwa = await jmc.getWalletAddress(ton.address(USER_WALLET_ADDRESS));
    const jw = ton.JettonWallet.create(jwa);
    const jwc = client.open(jw);
    console.log('--> Output: Account Jetton Wallet Address:', jwa.toString(wc));
    console.log('--> Output: Account Jetton Balance:', +(await jwc.getBalance()).toString() / 1e9);
  }
})();