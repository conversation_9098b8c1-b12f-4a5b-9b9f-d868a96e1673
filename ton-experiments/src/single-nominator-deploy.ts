import * as path from "node:path";
import * as fs from "node:fs";

import * as ton from "@ton/ton";
import * as tonCrypto from "@ton/crypto";
import { sleep } from "@ton/blueprint";
import * as dotenv from "dotenv";

import { _mnemonicFileToKeyPair } from "./utils";

(async function deploySingleNominator() {
  dotenv.config();
  const client = new ton.TonClient({ endpoint: process.env.TON_API });

  const SENDER_MNEMONIC_FILE = process.argv[2];

  // Create mock validator address
  const validatorMnemonic = await tonCrypto.mnemonicNew();
  const validatorKeyPair =
    await tonCrypto.mnemonicToWalletKey(validatorMnemonic);
  const validatorAddress = ton.WalletContractV5R1.create({
    publicKey: validatorKeyPair.publicKey,
  }).address;

  // Load user wallet using mnemonic file
  const senderKeyPair = await _mnemonicFileToKeyPair(
    path.resolve(__dirname, "..", SENDER_MNEMONIC_FILE),
  );
  const senderWallet = ton.WalletContractV5R1.create({
    publicKey: senderKeyPair.publicKey,
  });
  const walletContract = client.open(senderWallet);
  const walletSender = walletContract.sender(senderKeyPair.secretKey);
  const seqno = await walletContract.getSeqno();

  // Create contract data
  const code = ton.Cell.fromBoc(
    fs.readFileSync(
      path.resolve(__dirname, "..", "contracts-build/single-nominator.cell"),
    ),
  )[0];
  const data = ton
    .beginCell()
    .storeAddress(walletContract.address) // owner address
    .storeAddress(validatorAddress) // validator address
    .endCell();
  const workchain = 0; // deploy to workchain 0
  // Compute contract address from initial data
  const address = ton.contractAddress(workchain, { code, data });

  console.log(
    "--> Contract address:",
    address.toString({ testOnly: true, bounceable: true }),
  );
  if (await client.isContractDeployed(address)) {
    return console.log("--> Contract is already deployed");
  } else {
    console.log("--> Deploying contract...");
  }

  await sleep(5000);

  await client
    .provider(address, { code, data }) // contract
    .internal(walletSender, { value: "0.1" }); // wallet send message
  await sleep(5000);

  // Watch transaction confirmation
  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Checking transaction confirmation...", count);
    await sleep(5000);
    currentSeqno = await walletContract.getSeqno();
    if (count >= 10) {
      return console.error("--> Error: Cannot confirm transaction.");
    }
  }
  return console.log("--> Deploy transaction confirmed!");
})();
