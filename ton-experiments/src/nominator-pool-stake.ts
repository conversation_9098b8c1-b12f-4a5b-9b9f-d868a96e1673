import * as ton from "@ton/ton";

import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";
import {sleep} from "@ton/blueprint";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const SENDER_MNEMONIC_FILE = process.argv[2];
  const NOMINATOR_POOL_ADDRESS = process.argv[3];
  const STAKE_AMOUNT = process.argv[4] || "1.1"; // Stake 1 + Stake fee 0.1

  console.log('--> SENDER_MNEMONIC_FILE:', SENDER_MNEMONIC_FILE);
  console.log('--> NOMINATOR_POOL_ADDRESS:', NOMINATOR_POOL_ADDRESS);
  console.log('--> STAKE_AMOUNT:', STAKE_AMOUNT);

  const keyPairFrom = await _mnemonicFileToKeyPair(SENDER_MNEMONIC_FILE);

  const walletSender = ton.WalletContractV5R1.create({publicKey: keyPairFrom.publicKey});
  const walletSenderContractOpened = client.open(walletSender);

  // Sign the tx
  console.log(`--> Staking ${STAKE_AMOUNT} TON from ${walletSender.address.toString({ testOnly: true, bounceable: false })} to ${NOMINATOR_POOL_ADDRESS}`);
  let seqno = await walletSenderContractOpened.getSeqno();
  await walletSenderContractOpened.sendTransfer({
    seqno,
    messages: [ton.internal({
      value: STAKE_AMOUNT,
      to: ton.Address.parse(NOMINATOR_POOL_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
      body: 'd',    // deposit
    })],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
    secretKey: keyPairFrom.secretKey,
  });

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction to confirm...", count);
    await sleep(2000);
    currentSeqno = await walletSenderContractOpened.getSeqno();
  }
  console.log("--> Transaction confirmed!");
})();