import * as dotenv from "dotenv";
import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import {createWalletTransferV3} from "@ton/ton/dist/wallets/signing/createWalletTransfer";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const VESTING_OWNER_MNEMONIC_FILE = process.argv[2];
  const VESTING_CONTRACT_ADDRESS = process.argv[3];
  const DESTINATION_ADDRESS = process.argv[4];
  const AMOUNT = process.argv[5] || "1";

  console.log('--> VESTING_OWNER_MNEMONIC_FILE:', VESTING_OWNER_MNEMONIC_FILE);
  console.log('--> VESTING_CONTRACT_ADDRESS:', VESTING_CONTRACT_ADDRESS);
  console.log('--> NOMINATOR_POOL_ADDRESS:', DESTINATION_ADDRESS);
  console.log('--> AMOUNT:', AMOUNT);

  const vestingOwnerKeypair = await _mnemonicFileToKeyPair(VESTING_OWNER_MNEMONIC_FILE);
  const vestingOwnerWallet = ton.WalletContractV5R1.create({publicKey: vestingOwnerKeypair.publicKey});
  const vestingOwnerWalletOpened = client.open(vestingOwnerWallet);

  console.log('--> Transferring...')

  /**
   * Utilize the ton library to build the internal message
   * to transfer TON from vesting contract to nominator pool
   */
  const internalMessage = createWalletTransferV3(
    {
      seqno: 0,
      sendMode: 1,
      walletId: 0,
      messages: [
        ton.internal({
          to: ton.address(DESTINATION_ADDRESS),
          value: AMOUNT,
          bounce: true,
        })
      ],
      secretKey: vestingOwnerKeypair.secretKey
    }
  ).beginParse().loadRef()

  const seqno = await vestingOwnerWalletOpened.getSeqno();

  await client.provider(ton.address(VESTING_CONTRACT_ADDRESS))
    .internal(
      vestingOwnerWalletOpened.sender(vestingOwnerKeypair.secretKey),
      {
        value: "0.05", // to pay the gas
        sendMode: 1,
        body: ton.beginCell()
          .storeUint(0xa7733acd, 32)
          .storeUint(0, 64)
          .storeUint(3, 8)
          .storeRef(internalMessage)
          .endCell()
      }
    )

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction get confirmed...", count);
    await sleep(3000);
    currentSeqno = await vestingOwnerWalletOpened.getSeqno();
  }
  console.log("--> Transaction confirmed!");
})();