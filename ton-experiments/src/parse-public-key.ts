import * as ton from "@ton/ton";
import * as dotenv from "dotenv";

/**
 * Parse mnemonic file
 * - output Address
 * - also check contract deployed and balance
 */
async function parseMnemonic() {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const PUBLIC_KEY = process.argv[2]; // public key hex

  const wallet3 = ton.WalletContractV3R2.create({
    workchain: 0,
    publicKey: Buffer.from(PUBLIC_KEY.replace('0x', ''), 'hex')
  });
  const contract3 = client.open(wallet3);
  console.log(
    '--> V3:',
    contract3.address.toString({testOnly: true, bounceable: false}),
    await client.isContractDeployed(wallet3.address),
    +((await contract3.getBalance()).toString()) / 10 ** 9
  );

  const wallet4 = ton.WalletContractV4.create({
    workchain: 0,
    publicKey: Buffer.from(PUBLIC_KEY.replace('0x', ''), 'hex')
  });
  const contract4 = client.open(wallet4);
  console.log(
    '--> V4:',
    contract4.address.toString({testOnly: true, bounceable: false}),
    await client.isContractDeployed(wallet4.address),
    +((await contract4.getBalance()).toString()) / 10 ** 9
  );

  const wallet5 = ton.WalletContractV5R1.create({
    workChain: 0,
    publicKey: Buffer.from(PUBLIC_KEY.replace('0x', ''), 'hex')
  });
  const contract5 = client.open(wallet5);
  console.log(
    '--> V5:',
    contract5.address.toString({testOnly: true, bounceable: false}),
    await client.isContractDeployed(wallet5.address),
    +((await contract5.getBalance()).toString()) / 10 ** 9
  );
}

parseMnemonic();