import * as ton from "@ton/ton";
import * as dotenv from "dotenv";
import {_mnemonicFileToKeyPair} from "./utils";
import {sleep} from "@ton/blueprint";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const SENDER_MNEMONIC_FILE = process.argv[2];
  const NOMINATOR_POOL_ADDRESS = process.argv[3];

  const keyPairFrom = await _mnemonicFileToKeyPair(SENDER_MNEMONIC_FILE);

  const walletSender = ton.WalletContractV5R1.create({publicKey: keyPairFrom.publicKey});
  const walletSenderContractOpened = client.open(walletSender);

  // Sign the tx
  console.log(`--> Unstaking ALL TON from ${NOMINATOR_POOL_ADDRESS} to ${walletSender.address.toString({ testOnly: true, bounceable: false })} `);
  const seqno = await walletSenderContractOpened.getSeqno();
  await walletSenderContractOpened.sendTransfer({
    seqno,
    messages: [ton.internal({
      value: "1",
      to: ton.Address.parse(NOMINATOR_POOL_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
      body: 'w',    // deposit
    })],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
    secretKey: keyPairFrom.secretKey,
  });


  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction to confirm...", count);
    await sleep(2000);
    currentSeqno = await walletSenderContractOpened.getSeqno();
  }
  console.log("--> Transaction confirmed!");
})();