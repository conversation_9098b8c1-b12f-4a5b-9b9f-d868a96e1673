import {_mnemonicFileToKeyPair} from "./utils";
import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const MNEMONIC_FILE = process.argv[2];
  const SINGLE_NOMINATOR_ADDRESS = process.argv[3];
  const WITHDRAW_AMOUNT = +process.argv[4] || 1; // Remember to add FEE

  const keyPairFrom = await _mnemonicFileToKeyPair(MNEMONIC_FILE);

  const walletFrom = ton.WalletContractV5R1.create({publicKey: keyPairFrom.publicKey});

  // Create contract provider to for sending tx
  const contractFrom = client.open(walletFrom);
  await sleep(5000);

  // Sign the tx
  console.log(`--> Withdrawing ${WITHDRAW_AMOUNT} TON from ${SINGLE_NOMINATOR_ADDRESS} to ${walletFrom.address.toString({
    testOnly: true,
    bounceable: false
  })}`);
  console.log(`--> Sending transaction...`);
  await contractFrom.sendTransfer({
    secretKey: keyPairFrom.secretKey,
    seqno: await contractFrom.getSeqno(),
    messages: [ton.internal({
      value: "0.1",
      to: ton.Address.parse(SINGLE_NOMINATOR_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
      body: ton.beginCell()
        .storeUint(0x1000, 32) // op , withdraw = 0x1000
        .storeUint(0, 64) // query_id
        .storeCoins(WITHDRAW_AMOUNT * 1e9) // withdraw amount in grams
        .endCell()
    })],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
  });
  await sleep(5000);

  console.log('--> Checking transaction confirmation...');
  let seqno = await contractFrom.getSeqno();
  await sleep(5000);

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Checking transaction confirmation...", count);
    await sleep(5000);
    currentSeqno = await contractFrom.getSeqno();
    if (count >= 10) {
      return console.error('--> Error: Cannot confirm transaction.')
    }
  }
  return console.log("--> Deploy transaction confirmed!");
})();