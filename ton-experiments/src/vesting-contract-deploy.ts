import fs from "node:fs";

import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  // Ref from: (vesting-contract.fc)
  // CONDITIONS:
  // vesting_total_duration > 0
  // vesting_total_duration <= 135 years (2^32 seconds)
  // unlock_period > 0
  // unlock_period <= vesting_total_duration
  // cliff_duration >= 0
  // cliff_duration < vesting_total_duration
  // vesting_total_duration mod unlock_period == 0
  // cliff_duration mod unlock_period == 0

  const VESTING_OWNER_MNEMONIC_FILE = process.argv[2];
  const VESTING_SENDER_MNEMONIC_FILE = process.argv[3];
  const VESTING_TOTAL_DURATION = process.argv[4] || "3600"; // second, optional
  const UNLOCK_PERIOD = process.argv[5] || "3600"; // second, optional
  const CLIFF_DURATION = process.argv[6] || "0"; // second, optional
  const VESTING_TOTAL_AMOUNT = process.argv[7] || "1"; // TON, optional

  console.log('--> VESTING_OWNER_MNEMONIC_FILE', VESTING_OWNER_MNEMONIC_FILE);
  console.log('--> VESTING_SENDER_MNEMONIC_FILE', VESTING_SENDER_MNEMONIC_FILE);
  console.log('--> VESTING_TOTAL_DURATION', VESTING_TOTAL_DURATION);
  console.log('--> UNLOCK_PERIOD', UNLOCK_PERIOD);
  console.log('--> CLIFF_DURATION', CLIFF_DURATION);
  console.log('--> VESTING_TOTAL_AMOUNT', VESTING_TOTAL_AMOUNT);

  const vestingOwnerKeypair = await _mnemonicFileToKeyPair(VESTING_OWNER_MNEMONIC_FILE);
  const vestingOwnerWallet = ton.WalletContractV5R1.create({publicKey: vestingOwnerKeypair.publicKey});
  const vestingSenderKeypair = await _mnemonicFileToKeyPair(VESTING_SENDER_MNEMONIC_FILE);
  const vestingSenderWallet = ton.WalletContractV5R1.create({publicKey: vestingSenderKeypair.publicKey});
  const code = ton.Cell.fromBoc(fs.readFileSync("contracts-build/vesting-contract.cell"))[0]; // compilation output from step 6
  const startAt = Math.round(+Date.now() / 1e3);
  const workchain = 0; // deploy to workchain 0

  console.log('--> Vesting owner address:', vestingOwnerWallet.address.toString({testOnly: true, bounceable: false}));
  console.log('--> Vesting sender address:', vestingSenderWallet.address.toString({testOnly: true, bounceable: false}));
  console.log('--> Vesting start time:', startAt);

  const data = ton.beginCell()
    .storeUint(0, 32) // stored_seqno
    .storeUint(0, 32) // stored_subwallet
    .storeBuffer(vestingOwnerKeypair.publicKey) // public_key
    .storeUint(0, 1) // empty whitelist
    .storeRef(
      ton.beginCell()
        .storeUint(startAt, 64) // vesting_start_time (Unix time)
        .storeUint(+VESTING_TOTAL_DURATION, 32) // vesting_total_duration
        .storeUint(+UNLOCK_PERIOD, 32) // unlock_period
        .storeUint(+CLIFF_DURATION, 32) // cliff_duration
        .storeCoins(+VESTING_TOTAL_AMOUNT * 1e9) // vesting_total_amount
        .storeAddress(vestingSenderWallet.address) // vesting_sender_address
        .storeAddress(vestingOwnerWallet.address) // owner_address
        .endCell()
    )
    .endCell();

  // Compute vesting contract address from code and data
  const vestingContractAddress = ton.contractAddress(workchain, {code, data});
  console.log("--> Vesting contract address:", vestingContractAddress.toString({testOnly: true, bounceable: true}));
  if (await client.isContractDeployed(vestingContractAddress)) {
    return console.log("--> Contract is already deployed");
  }

  const vestingOwnerWalletOpened = client.open(vestingOwnerWallet);
  const vestingOwnerClientSender = vestingOwnerWalletOpened.sender(vestingOwnerKeypair.secretKey);
  const seqno = await vestingOwnerWalletOpened.getSeqno();

  await client
    .provider(vestingContractAddress, {code, data})
    .internal(vestingOwnerClientSender, {value: "0.1"}); // a small amount to pay gas

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction get confirmed...", count);
    await sleep(3000);
    currentSeqno = await vestingOwnerWalletOpened.getSeqno();
  }
  console.log('--> Deploy transaction confirmed!');
})()