import fs from "node:fs";

import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const SENDER_MNEMONIC_FILE = process.argv[2];

  const code = ton.Cell.fromBoc(fs.readFileSync("contracts-build/nominator-pool.cell"))[0]; // compilation output from step 6
  const data = ton.beginCell()
    .storeUint(0, 8) // state
    .storeUint(0, 16) // nominators_count
    .storeCoins(0) // stake_amount_sent
    .storeCoins(0) // validator_amount
    .storeRef( // config
      ton.beginCell()
        .storeUint(0x2e14b3f32893f0906172194a9f5cf8b8b11d6cd25c4dde41dc963e5a890540c4, 256) // validator address
        .storeUint(1, 16) // validator_reward_share
        .storeUint(40, 16) // max_nominators_count
        .storeCoins(0) // min_validator_stake
        .storeCoins(1) // min_nominator_stake
        .endCell()
    )
    .storeDict(ton.Dictionary.empty()) // nominators
    .storeDict(ton.Dictionary.empty()) // withdraw_requests
    .storeUint(Math.floor(Math.random() * 10000), 32) //  stake_at
    .storeUint(0, 256) // saved_validator_set_hash
    .storeUint(0, 8) // validator_set_changes_count
    .storeUint(0, 32) // validator_set_change_time
    .storeUint(32, 32) // stake_held_for
    .storeDict(ton.Dictionary.empty()) // config_proposal_votings
    .endCell();
  const workchain = 0; // deploy to workchain 0
  const address = ton.contractAddress(workchain, {code, data});

  console.log("--> Contract address:", address.toString({testOnly: true, bounceable: true}));
  if (await client.isContractDeployed(address)) {
    return console.log("--> Contract is already deployed");
  }

  const senderKeyPair = await _mnemonicFileToKeyPair(SENDER_MNEMONIC_FILE);
  const senderWallet = ton.WalletContractV5R1.create({publicKey: senderKeyPair.publicKey});
  const walletContract = client.open(senderWallet);
  const walletSender = walletContract.sender(senderKeyPair.secretKey);
  const seqno = await walletContract.getSeqno();

  await client
    .provider(address, {code, data})
    .internal(walletSender, {value: "0.1"});

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction to confirm...", count);
    await sleep(2000);
    currentSeqno = await walletContract.getSeqno();
  }
  console.log("--> Transaction confirmed!");
})();