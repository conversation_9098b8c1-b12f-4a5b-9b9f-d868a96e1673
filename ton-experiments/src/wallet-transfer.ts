import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";
import * as nacl from "tweetnacl";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const SENDER_MNEMONIC_FILE = process.argv[2];
  const DESTINATION_ADDRESS = process.argv[3];
  const VALUE = process.argv[4];
  const BODY = process.argv[5] || undefined;

  console.log('--> SENDER_MNEMONIC_FILE:', SENDER_MNEMONIC_FILE);
  console.log('--> DESTINATION_ADDRESS:', DESTINATION_ADDRESS);
  console.log('--> VALUE:', VALUE);
  console.log('--> BODY:', BODY);

  const keyPairFrom = await _mnemonicFileToKeyPair(SENDER_MNEMONIC_FILE);
  const to_address = ton.Address.parse(DESTINATION_ADDRESS);
  const senderWallet = ton.WalletContractV5R1.create({publicKey: keyPairFrom.publicKey});
  const senderContractOpened = client.open(senderWallet);
  const destinationAddress = ton.Address.parseFriendly(DESTINATION_ADDRESS);
  console.log('--> Parse destination address:', 'bounceable', destinationAddress.isBounceable, 'isTestOnly:', destinationAddress.isTestOnly);

  // Sign the tx
  let seqno = await senderContractOpened.getSeqno();
  const signedTxCell = await senderContractOpened.createTransfer({
    seqno,
    messages: [ton.internal({value: VALUE, to: to_address, bounce: destinationAddress.isBounceable, body: BODY})],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
    signer: async (cell: ton.Cell) => {
      return Buffer.from(
        nacl.sign.detached(
          new Uint8Array(cell.hash()),
          new Uint8Array(keyPairFrom.secretKey)
        )
      );
    },
  });

  await senderContractOpened.send(ton.Cell.fromBase64(signedTxCell.toBoc().toString('base64')));
  console.log('==> Sent from:', senderWallet.address.toString({
    testOnly: true,
    bounceable: false
  }), 'to:', DESTINATION_ADDRESS, 'amount:', VALUE);

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction get confirmed...", count);
    await sleep(3000);
    currentSeqno = await senderContractOpened.getSeqno();
  }

  console.log('--> Deploy transaction confirmed!');
})();