import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const VESTING_SENDER_MNEMONIC_FILE = process.argv[2];
  const VESTING_CONTRACT_ADDRESS = process.argv[3];
  const WHITELIST_ADDRESS = process.argv[4];

  console.log('VESTING_SENDER_MNEMONIC_FILE', VESTING_SENDER_MNEMONIC_FILE);
  console.log('VESTING_CONTRACT_ADDRESS', VESTING_CONTRACT_ADDRESS);
  console.log('WHITELIST_ADDRESS', WHITELIST_ADDRESS);

  const vestingSenderKeypair = await _mnemonicFileToKeyPair(VESTING_SENDER_MNEMONIC_FILE);
  const vestingSenderWallet = ton.WalletContractV5R1.create({publicKey: vestingSenderKeypair.publicKey});
  console.log('--> Vesting sender address:', vestingSenderWallet.address.toString({testOnly: true, bounceable: false}));

  const vestingSenderContractOpened = client.open(vestingSenderWallet);

  const seqno = await vestingSenderContractOpened.getSeqno();

  await vestingSenderContractOpened.sendTransfer({
    secretKey: vestingSenderKeypair.secretKey,
    seqno,
    messages: [ton.internal({
      value: "0.05", // small amount to pay gas
      to: ton.Address.parse(VESTING_CONTRACT_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
      body: ton.beginCell()
        .storeUint(0x7258a69b, 32) // op::add_whitelist
        .storeUint(0, 64) // query_id;
        .storeAddress(ton.Address.parse(WHITELIST_ADDRESS))
        .endCell(),
    })],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
  });

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction get confirmed...", count);
    await sleep(3000);
    currentSeqno = await vestingSenderContractOpened.getSeqno();
  }
  return console.log("--> Transaction confirmed!");
})()
