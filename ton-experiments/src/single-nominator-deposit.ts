import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const MNEMONIC_FILE = process.argv[2];
  const SINGLE_NOMINATOR_ADDRESS = process.argv[3];
  const VALUE = process.argv[4] || "1.5"; // Remember to add FEE

  const keyPairFrom = await _mnemonicFileToKeyPair(MNEMONIC_FILE);

  const walletFrom = ton.WalletContractV5R1.create({publicKey: keyPairFrom.publicKey});

  // Create contract provider to for sending tx
  const contractFrom = client.open(walletFrom);

  // Sign the tx
  console.log(`--> Sending ${VALUE} TON from ${walletFrom.address.toString({
    testOnly: true,
    bounceable: false
  })} to ${SINGLE_NOMINATOR_ADDRESS} to stake`);
  await contractFrom.sendTransfer({
    secretKey: keyPairFrom.secretKey,
    seqno: await contractFrom.getSeqno(),
    messages: [ton.internal({
      value: VALUE,
      to: ton.Address.parse(SINGLE_NOMINATOR_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
    })],
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
  });
  await sleep(5000);

  console.log('--> Checking transaction confirmation...');

  let seqno = await contractFrom.getSeqno();
  await sleep(5000);

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Checking transaction confirmation...", count);
    await sleep(5000);
    currentSeqno = await contractFrom.getSeqno();
    if (count >= 10) {
      return console.error('--> Error: Cannot confirm transaction.')
    }
  }
  return console.log("--> Deploy transaction confirmed!");
})();