import * as ton from "@ton/ton";
import * as dotenv from "dotenv";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const VESTING_CONTRACT_ADDRESS = process.argv[2];
  const ADDRESS_TO_CHECK = process.argv[3];

  console.log('--> Vesting contract:', VESTING_CONTRACT_ADDRESS);
  console.log('--> Address to check:', ADDRESS_TO_CHECK);

  const vestingContractAddress = ton.Address.parse(VESTING_CONTRACT_ADDRESS);
  const addressToCheck = ton.Address.parse(ADDRESS_TO_CHECK);

  let result = await client.runMethodWithError(vestingContractAddress, "is_whitelisted", [{
    type: 'slice',
    cell: ton.beginCell().storeAddress(addressToCheck).endCell(),
  }]);
  if (result.exit_code !== 0) {
    return console.error('--> Error: Exit code:', result.exit_code);
  }
  console.log('--> Result:', result.stack.readBoolean());
})()