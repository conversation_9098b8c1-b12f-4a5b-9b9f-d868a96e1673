import * as ton from "@ton/ton";
import {sleep} from "@ton/blueprint";
import {createWalletTransferV3} from "@ton/ton/dist/wallets/signing/createWalletTransfer";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";
import * as nacl from "tweetnacl";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const VESTING_OWNER_MNEMONIC_FILE = process.argv[2];
  const VESTING_CONTRACT_ADDRESS = process.argv[3];
  const NOMINATOR_POOL_ADDRESS = process.argv[4];
  const STAKE_AMOUNT = process.argv[5] || "1.1";

  console.log('--> VESTING_OWNER_MNEMONIC_FILE:', VESTING_OWNER_MNEMONIC_FILE);
  console.log('--> VESTING_CONTRACT_ADDRESS:', VESTING_CONTRACT_ADDRESS);
  console.log('--> NOMINATOR_POOL_ADDRESS:', NOMINATOR_POOL_ADDRESS);
  console.log('--> VALUE:', STAKE_AMOUNT);

  const vestingOwnerKeypair = await _mnemonicFileToKeyPair(VESTING_OWNER_MNEMONIC_FILE);
  const vestingOwnerWallet = ton.WalletContractV5R1.create({publicKey: vestingOwnerKeypair.publicKey});
  const vestingOwnerWalletOpened = client.open(vestingOwnerWallet);

  console.log('--> Send staking from vesting contract...')

  /**
   * Utilize the ton library to build the internal message
   * to transfer TON from vesting contract to nominator pool
   */
  const internalMessage = await createWalletTransferV3(
    {
      seqno: 0,
      sendMode: 1,
      walletId: 0,
      messages: [
        ton.internal({
          to: ton.address(NOMINATOR_POOL_ADDRESS),
          value: STAKE_AMOUNT,
          bounce: true,
          body: "d",
        })
      ],
      signer: async (cell: ton.Cell) => {
        return Buffer.from(
          nacl.sign.detached(
            new Uint8Array(cell.hash()),
            new Uint8Array(vestingOwnerKeypair.secretKey)
          )
        );
      }
    }
  ).then(c => c.beginParse().loadRef());

  const seqno = await vestingOwnerWalletOpened.getSeqno();

  await vestingOwnerWalletOpened.sendTransfer({
    seqno,
    sendMode: ton.SendMode.PAY_GAS_SEPARATELY,
    messages: [ton.internal({
      value: "0.1",
      to: ton.address(VESTING_CONTRACT_ADDRESS),
      bounce: true, // allow bouncing to receive back TON if any errors
      body: ton.beginCell()
        .storeUint(0xa7733acd, 32)
        .storeUint(0, 64)
        .storeUint(3, 8) // send mode
        .storeRef(internalMessage)
        .endCell(),
    })],
    signer: async (cell: ton.Cell) => {
      return Buffer.from(
        nacl.sign.detached(
          new Uint8Array(cell.hash()),
          new Uint8Array(vestingOwnerKeypair.secretKey)
        )
      );
    }
  });

  let currentSeqno = seqno;
  let count = 0;
  while (currentSeqno === seqno) {
    count++;
    console.log(">> Waiting for transaction get confirmed...", count);
    await sleep(3000);
    currentSeqno = await vestingOwnerWalletOpened.getSeqno();
  }
  console.log("--> Transaction confirmed!");
})();