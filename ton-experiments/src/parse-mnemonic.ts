import * as ton from "@ton/ton";
import * as dotenv from "dotenv";

import {_mnemonicFileToKeyPair} from "./utils";

/**
 * Parse mnemonic file
 * - output Address
 * - also check contract deployed and balance
 */
async function parseMnemonic() {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const MNEMONIC_FILE = process.argv[2];

  const keyPair = await _mnemonicFileToKeyPair(MNEMONIC_FILE);
  const wallet = ton.WalletContractV5R1.create({publicKey: keyPair.publicKey});
  const contract = client.open(wallet);
  console.log('--> Check contract deployed:', await client.isContractDeployed(wallet.address));
  console.log('--> Address:', contract.address.toString({testOnly: true, bounceable: false}));
  console.log('--> Balance:', +((await contract.getBalance()).toString()) / 10 ** 9);
}

parseMnemonic();