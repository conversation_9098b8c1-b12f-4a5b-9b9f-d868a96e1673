import * as ton from "@ton/ton";
import * as dotenv from "dotenv";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const VESTING_CONTRACT_ADDRESS = process.argv[2];
  const START_TIME = process.argv[3];
  const AT_SECOND = process.argv[4];

  console.log('--> VESTING_CONTRACT_ADDRESS:', VESTING_CONTRACT_ADDRESS);
  console.log('--> START_TIME:', START_TIME);
  console.log('--> AT_SECOND:', AT_SECOND);

  const at = START_TIME || AT_SECOND ? +START_TIME + +AT_SECOND : Math.round(Date.now() / 1e3);
  console.log('--> at:', at);

  const vestingContractAddress = ton.Address.parse(VESTING_CONTRACT_ADDRESS);

  let result = await client.runMethodWithError(vestingC<PERSON>ractAddress, "get_locked_amount", [{type: 'int', value: at}]);
  if (result.exit_code !== 0) {
    console.error(`Error: Exit code: ${result.exit_code}`);
  } else {
    const n = result.stack.readNumber();
    console.log(`--> Locked amount: ${n} gram = ${n / 1e9} TON`);
  }
})()