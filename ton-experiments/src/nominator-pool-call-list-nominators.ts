import * as ton from "@ton/ton";
import * as dotenv from "dotenv";

(async () => {
  dotenv.config();
  const client = new ton.TonClient({endpoint: process.env.TON_API});

  const NOMINATOR_POOL_ADDRESS = process.argv[2];

  let result = await client.runMethodWithError(ton.address(NOMINATOR_POOL_ADDRESS), "list_nominators");
  if (result.exit_code) {
    return console.error('--> Error: Exit code:', result.exit_code);
  }
  const list = result.stack.pop() as ton.Tuple;
  if (!list?.items?.length) {
    return console.log('--> Nominators: 0');
  }
  console.log('--> Nominators:', list.items.length);
  for (let i = 0; i < list.items.length; i++) {
    const [ad, am] = list.items[i] as unknown as [bigint, bigint, ...any];
    const address = ton.Address.parseRaw(`0:${ad.toString(16)}`);
    const amount = ton.fromNano(am).toString();
    console.log(`--> Nominator ${i}:`, address.toString({
      testOnly: true,
      bounceable: true
    }), 'raw:', address.toRawString(), 'amount:', amount);
  }
})();