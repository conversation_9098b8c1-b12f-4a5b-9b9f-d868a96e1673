## Introduction
Ton experiment with `ton`

## wallet UI
https://testnet.tonscan.org/

## Setup
```bash
npm ci
```

## Flow to activate a wallet

### Step 1: Create mnemonic

```bash
npx ts-node . create-mn

# Output
--> Mnemonic file: ton_wallet_mnemonic_1724403136520
```

Parse new created mnemonic and get wallet status

```shell
npx ts-node src/parse-mnemonic.ts ton_wallet_mnemonic_1724403136520

# Output
--> Check contract deployed: false
--> Address: 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8
--> Balance: 0
```

### Step 2: Pre-fund new created wallet

Send some TON to new created wallet

```shell
npx ts-node src/wallet-transfer.ts ton_wallet_1_mnemonic 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8 1

# Output
--> Check walletFrom contract deployed: true
--> Check walletTo contract deployed: false
--> Signing tx using @noble/ed25519
==> Sent from: 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG to: 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8 amount: 1
```

Check the balance to make sure that the transfer is success

```bash
npx ts-node src/parse-mnemonic.ts ton_wallet_mnemonic_1724403136520

# Output
--> Check contract deployed: false
--> Address: 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8
--> Balance: 0.9996
```

### Step 3: Transfer back for activating new wallet

Transfer some TON back to pre-fund wallet

```bash
npx ts-node src/wallet-transfer.ts ton_wallet_mnemonic_1724403136520 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG 0.9

# Output
--> Check walletFrom contract deployed: false
--> Check walletTo contract deployed: true
==> Sent from: 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8 to: 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG amount: 0.9
```

Check the new created wallet again and seeing that the contract status is deployed.

```bash
npx ts-node src/parse-mnemonic.ts ton_wallet_mnemonic_1724403136520

# Output
--> Check contract deployed: true
--> Address: 0QB3lIKnKTIwNCx3ZIaJlmiXBoFpRPse8yHLuOJD_m_sl9S8
--> Balance: 0.094119996
```

## Staking with Nominator Pool

Build the contract

```shell
npx func-js contracts/nominator-pool.fc --boc contracts-build/nominator-pool.cell 
```

Deploy a new testing nominator pool contract

```shell
npx ts-node src/nominator-pool-deploy.ts ton_wallet_1_mnemonic

# Output
--> Contract address: kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx
>> Waiting for deploy transaction to confirm... 1
>> Waiting for deploy transaction to confirm... 2
>> Waiting for deploy transaction to confirm... 3
--> Deploy transaction confirmed!
```

NOTE: the testing contract take 0.1 TON for every success staking

Double-check the contract is deployed by calling its method `list_nominators`

```shell
npx ts-node src/nominator-pool-call-list-nominators.ts kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx

# Output
--> Nominators: 0
```

Staking command

```shell
# Stake 1 TON by sending 1.1 (0.1 TON for deposit testing fee)
npx ts-node src/nominator-pool-stake.ts ton_wallet_1_mnemonic kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx 1.1

# Output
--> Sending 1.1 TON from EQAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxBxJ to EQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_Ubu7 to stake 
```

Checking staking success by calling `list_nominators` method

```shell
npx ts-node src/nominator-pool-call-list-nominators.ts kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx

# Output
--> Nominators: 1
--> Nominator 0: 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG raw: 0:2e14b3f32893f0906172194a9f5cf8b8b11d6cd25c4dde41dc963e5a890540c4 amount: 1
```

Unstake

```shell
npx ts-node src/nominator-pool-unstake.ts ton_wallet_1_mnemonic kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx

# Output
--> Sending 1 TON from EQAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxBxJ to kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx to unstake ALL TON
```

You can call the `list_nomiators` method to confirm unstake successful

```shell
npx ts-node src/nominator-pool-call-list-nominators.ts kQDHc3BZELn3RXrzlf-lqX4iCiEWepPw7dE3krkUWos_UQAx

# Output
--> Nominators: 0
```

## Single Nominator

Build Single Nominator contract

```shell
npx func-js contracts/single-nominator.fc --boc contracts-build/single-nominator.cell

# Output
Compiling using func v0.4.4
Compiled successfully!
Written output files.
```

Deploy a new Single Nominator contract

```shell
npx ts-node src/single-nominator-deploy.ts ton_wallet_1_mnemonic

# Output
--> Endpoint: https://ton.access.orbs.network/4411c0ff5Bd3F8B62C092Ab4D238bEE463E64411/1/testnet/toncenter-api-v2/jsonRPC
--> Contract address: kQCvFrkAUzGd0Oal-EfBMvScNlOVqkmlD4VJcDwye-OKxVgv
--> Deploying contract...
>> Checking transaction confirmation... 1
--> Deploy transaction confirmed!
```

Deposit to the Single Nominator contract

```shell
npx ts-node src/single-nominator-deposit.ts ton_wallet_1_mnemonic kQCvFrkAUzGd0Oal-EfBMvScNlOVqkmlD4VJcDwye-OKxVgv 3

# Output
--> Endpoint: https://ton.access.orbs.network/4411c0ff5Bd3F8B62C092Ab4D238bEE463E64411/1/testnet/toncenter-api-v2/jsonRPC
--> Sending 3 TON from 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG to kQCvFrkAUzGd0Oal-EfBMvScNlOVqkmlD4VJcDwye-OKxVgv to stake
--> Checking transaction confirmation...
>> Checking transaction confirmation... 3
--> Deploy transaction confirmed!
```

Withdraw from Single Nominator contract

```shell
npx ts-node src/single-nominator-withdraw.ts ton_wallet_1_mnemonic kQCvFrkAUzGd0Oal-EfBMvScNlOVqkmlD4VJcDwye-OKxVgv 2

# Output
--> Endpoint: https://ton.access.orbs.network/4411c0ff5Bd3F8B62C092Ab4D238bEE463E64411/1/testnet/toncenter-api-v2/jsonRPC
--> Withdrawing 2 TON from kQCvFrkAUzGd0Oal-EfBMvScNlOVqkmlD4VJcDwye-OKxVgv to 0QAuFLPzKJPwkGFyGUqfXPi4sR1s0lxN3kHclj5aiQVAxPoG
--> Sending transaction...
--> Checking transaction confirmation...
>> Checking transaction confirmation... 1
--> Deploy transaction confirmed!
```

## Jetton

Get Jetton wallet address for an user wallet
```shell
npx ts-node . jetton-wallet kQDK48n0uDVmLD4g_3Vu36tOm_taHHTNuu_BaOV7zn0IO3iN 0QBGlr4JywadwEmz6XXImwcjx81V-HyPZPpiyluxMakTWG77

# Result
--> Input: Jetton Master Address: kQDK48n0uDVmLD4g_3Vu36tOm_taHHTNuu_BaOV7zn0IO3iN
--> Input: Account Address: 0QBGlr4JywadwEmz6XXImwcjx81V-HyPZPpiyluxMakTWG77
--> Output: Account Jetton Wallet Address: 0QAszt19Qvdy3DYECH1L0iEQMCrh45iT3Wr5yID3LQQQrfAr
--> Output: Account Jetton Balance: 99
```

## FAQ
Q: What are the different between different wc(s)?
A: wc: 0 is BaseChain for everyday transaction while wc: -1 is master chain mainly used for network management, governance, and maintaining consensus.

Q: How many wallet addresses can be owned by same public key?
A: many, the addresses will be different when wc, isUserFriendly, isTestOnly, isUrlSafe and isBounceable are different in wallet creation

Q: How the balance share by address?
A: the balance will be shared all the address under same public key and wc but no cross chain.

Q: Can the transfer from wc -1 to wc 0
A: Yes

Q: What is seqNo?
A: The unique count with the wallet. If the seqNo is incorrect, the transaction will be failed.

Q: the cost for deploying wallet?
A: testnet - wc 0: 0.*********
A: testnet - wc -1: 0.*********
A: mainnet - wc 0: 0.0015044
A: mainnet - wc -1: 0.03761