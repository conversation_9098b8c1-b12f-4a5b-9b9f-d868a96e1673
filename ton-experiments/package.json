{"name": "ton-experiment", "version": "1.0.0", "description": "", "main": "index.ts", "scripts": {"start": "ts-node"}, "author": "", "license": "ISC", "dependencies": {"@noble/ed25519": "^2.1.0", "@orbs-network/ton-access": "^2.3.3", "@ton/blueprint": "^0.22.0", "@ton/core": "^0.56.3", "@ton/crypto": "^3.2.0", "@ton/ton": "^14.0.0", "@types/bluebird": "^3.5.42", "bluebird": "^3.7.2", "dotenv": "^16.4.5", "func-js": "^0.1.1", "tweetnacl": "^1.0.3"}, "devDependencies": {"@types/node": "^22.5.3", "ts-node": "^10.9.2", "typescript": "^5.5.4"}}